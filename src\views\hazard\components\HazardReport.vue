<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位上报数据</div>
    <div class="p-4 panel-content">
      <div class="flex gap-4">
        <div class="flex flex-col gap-3">
          <div class="data-item">
            <div class="data-label">近已年累计上报</div>
            <div class="data-value-wrap">
              <span class="data-value">34</span>
              <span class="data-unit">个</span>
            </div>
          </div>
          <div class="data-item">
            <div class="data-label">近三年累计上报</div>
            <div class="data-value-wrap">
              <span class="data-value">36</span>
              <span class="data-unit">个</span>
            </div>
          </div>
          <div class="data-item">
            <div class="data-label">近五年累计上报</div>
            <div class="data-value-wrap">
              <span class="data-value">36</span>
              <span class="data-unit">个</span>
            </div>
          </div>
        </div>
        <div class="chart-section">
          <div ref="chartRef" class="donut-chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

let data = [
  {
    label: '2024-01',
    value: 2,
  },
  {
    label: '2024-02',
    value: 3,
  },
  {
    label: '2024-03',
    value: 4,
  },
  {
    label: '2024-04',
    value: 2,
  },
  {
    label: '2024-05',
    value: 3,
  },
  {
    label: '2024-06',
    value: 2,
  },
  {
    label: '2024-07',
    value: 3,
  },
  {
    label: '2024-08',
    value: 2,
  },
  {
    label: '2024-09',
    value: 3,
  },
  {
    label: '2024-10',
    value: 2,
  },
  {
    label: '2024-11',
    value: 3,
  },
  {
    label: '2024-12',
    value: 2,
  },
  {
    label: '2025-01',
    value: 2,
  },
  {
    label: '2025-02',
    value: 1,
  },
]

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: ['2020-2022', '2022-2023', '2024-2025'],
    axisLine: {
      lineStyle: {
        color: '#fff',
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#fff',
      },
    },
  },
  series: [
    {
      name: '2020-2022',
      type: 'bar',
      data: [33340, 33340, 33340],
      barWidth: '20%',
      itemStyle: {
        color: '#55d0e0',
      },
    },
    {
      name: '2022-2023',
      type: 'bar',
      data: [33340, 33340, 33340],
      barWidth: '20%',
      itemStyle: {
        color: '#f7b731',
      },
    },
    {
      name: '2024-2025',
      type: 'bar',
      data: [33340, 33340, 33340],
      barWidth: '20%',
      itemStyle: {
        color: '#003f6f',
      },
    },
  ],
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';

.data-item {
  width: 160px;
  height: 64px;
  background: url('@/assets/hazard/hazard-report-bg.svg') no-repeat center;
  background-size: 100%;
}

.data-label {
  margin-top: 8px;
  font-family: Noto Sans SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  text-align: center;
  color: #99d5ff;
}

.data-value-wrap {
  text-align: center;
}

.data-value {
  font-family: Noto Sans SC;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  color: #ffffff;
}

.data-unit {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
}

.chart-section {
  width: 776px;
  height: 216px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
