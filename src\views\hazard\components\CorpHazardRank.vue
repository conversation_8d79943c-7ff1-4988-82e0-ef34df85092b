<template>
  <div class="panel-container-col">
    <div class="panel-header">企业重点防控点位排行</div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
    <svg style="visibility: hidden">
      <linearGradient id="textGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stop-color="#E19760"></stop>
        <stop offset="100%" stop-color="#E1976073"></stop>
      </linearGradient>
      <linearGradient id="barGradient" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="#E1976073"></stop>
        <stop offset="100%" stop-color="#E19760"></stop>
      </linearGradient>
    </svg>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const mockData = [
  {
    key: '中石油昆仑燃气',
    value: 4,
  },
  {
    key: '新奥燃气',
    value: 3,
  },
  {
    key: '华港燃气',
    value: 3,
  },
  {
    key: '东宏液化气',
    value: 2,
  },
  {
    key: '五申液化气',
    value: 2,
  },
  {
    key: '中海油雄安子午加气站',
    value: 2,
  },
  {
    key: '中油中泰雄安燃气',
    value: 1,
  },
  {
    key: '国能雄安综合能源',
    value: 1,
  },
  {
    key: '华润加油加气站',
    value: 1,
  },
]

const option = {
  grid: {
    top: 0,
    left: 40,
    bottom: 0,
    right: 0,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 18,
        fontFamily: 'my-num',
        fontWeight: 600,
        color: 'url(#textGradient)',
      },
      data: ['01', '02', '03', '04', '05', '06', '07', '08', '09'],
    },
    {
      inverse: true,
      offset: -30,
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        verticalAlign: 'bottom',
        lineHeight: 18,
        formatter: (_value: any, index: number) => {
          return mockData[index].value
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data: mockData.map(i => ({
        value: 0,
        key: i.key,
      })),
    },
  ],
  series: [
    {
      name: 'name',
      type: 'bar',
      barWidth: 8,
      showBackground: true,
      backgroundStyle: {
        color: 'transparent',
      },
      label: {
        show: true,
        color: '#fff',
        align: 'left',
        formatter: ({ data }: { data: { key: string; value: number } }) => {
          return data.key
        },
      },
      data: mockData.map(i => ({
        value: 0,
        key: i.key,
      })),
    },
    {
      name: 'value',
      type: 'bar',
      barWidth: 16,
      emphasis: {
        disabled: true,
      },
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(225, 151, 96, 0.15)',
      },
      itemStyle: {
        color: 'url(#barGradient)',
        borderWidth: 1,
        borderColor: 'rgba(225, 151, 96, 0.15)',
      },
      data: mockData.map(i => i.value),
      barGap: '70%',
    },

    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      colorBy: 'data',
      symbol: 'react',
      symbolPosition: 'end',
      symbolSize: [2, 18],
      symbolOffset: ['100%', 7],
      z: 10,
      itemStyle: {
        color: '#E19760',
      },
      data: mockData.map(i => i.value),
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value, null, { renderer: 'svg' })
  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = (option.yAxis[0] as any).data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })
  }, 2000) // 每隔2秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
